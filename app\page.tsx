"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Progress } from "@/components/ui/progress"
import { Upload, Download, Search, Database, AlertCircle, CheckCircle, RefreshCw } from "lucide-react"
import * as XLSX from "xlsx"
import Papa from "papaparse"

interface UniversityData {
  universityName: string
  universityCode: string
  majorName: string
  majorCode: string
  minRank: number
  minScore: number
  planCount: string
}

interface VolunteerItem {
  order: number
  universityName?: string
  universityCode: string
  majorName?: string
  majorCode: string
  isAdmitted?: boolean
  minRank?: number
  minScore?: number
  planCount?: string
  rankDiff?: number // 位次差值
  scoreDiff?: number // 分数差值
}

// 更新的CSV数据URL
const CSV_DATA_URLS = [
  "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/%E6%B5%99%E6%B1%9F%E7%9C%812025%E5%B9%B4%E6%99%AE%E9%80%9A%E7%B1%BB%E7%AC%AC%E4%B8%80%E6%AE%B5%E5%B9%B3%E8%A1%8C%E6%8A%95%E6%A1%A3%E5%88%86%E6%95%B0%E7%BA%BF%E8%A1%A8-NINEIBOwhxJBMTjPz7KzmVNnSnvoS1.csv",
  "https://photo.drhorry.com/file/1753070714854.csv", // 备用，虽然可能404
]

export default function GaokaoVolunteerQuery() {
  const [csvData, setCsvData] = useState<UniversityData[]>([])
  const [volunteerData, setVolunteerData] = useState<VolunteerItem[]>([])
  const [studentRank, setStudentRank] = useState<string>("")
  const [studentScore, setStudentScore] = useState<string>("")
  const [results, setResults] = useState<VolunteerItem[]>([])
  const [loading, setLoading] = useState(false)
  const [csvLoading, setCsvLoading] = useState(false)
  const [progress, setProgress] = useState(0)
  const [error, setError] = useState<string>("")
  const [debugInfo, setDebugInfo] = useState<string>("")
  const [dataStats, setDataStats] = useState<{
    total: number
    withRank: number
    withScore: number
    processed: number
  }>({ total: 0, withRank: 0, withScore: 0, processed: 0 })

  // 组件加载时自动尝试加载数据
  useEffect(() => {
    loadCsvData()
  }, [])

  // 尝试从多个URL加载CSV数据
  const loadCsvData = async () => {
    setCsvLoading(true)
    setError("")
    setDebugInfo("开始尝试加载CSV数据...")

    for (let i = 0; i < CSV_DATA_URLS.length; i++) {
      const url = CSV_DATA_URLS[i]
      try {
        setDebugInfo(`正在尝试第${i + 1}个数据源...\nURL: ${url}`)
        const response = await fetch(url)

        setDebugInfo((prev) => prev + `\nHTTP状态: ${response.status} ${response.statusText}`)

        if (!response.ok) {
          throw new Error(`HTTP错误: ${response.status} ${response.statusText}`)
        }

        setDebugInfo((prev) => prev + "\n✓ 成功获取数据，正在解析...")
        const csvText = await response.text()
        await processCsvData(csvText)
        setDebugInfo((prev) => prev + "\n✓ 数据加载完成！")
        setCsvLoading(false)
        return // 成功加载，退出循环
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : String(err)
        setDebugInfo((prev) => prev + `\n❌ 第${i + 1}个数据源失败: ${errorMessage}`)

        if (i === CSV_DATA_URLS.length - 1) {
          // 所有URL都失败了
          setError("无法从在线数据源加载录取数据，请手动上传CSV文件")
          setDebugInfo((prev) => prev + "\n\n⚠️ 所有在线数据源都无法访问，请使用手动上传功能")
        }
      }
    }
    setCsvLoading(false)
  }

  // 处理CSV数据的通用函数，根据新的Schema优化
  const processCsvData = async (csvText: string) => {
    return new Promise<void>((resolve, reject) => {
      setDebugInfo((prev) => prev + `\nCSV文件大小: ${csvText.length} 字符`)

      // 显示CSV文件的前几行用于调试
      const firstLines = csvText.split("\n").slice(0, 3).join("\n")
      setDebugInfo((prev) => prev + `\nCSV前3行内容:\n${firstLines}`)

      Papa.parse(csvText, {
        header: true,
        encoding: "UTF-8",
        skipEmptyLines: true,
        complete: (results) => {
          try {
            setDebugInfo((prev) => prev + `\nPapa Parse完成，共${results.data.length}行数据`)

            const data = results.data as any[]

            // 显示第一行数据的字段名
            if (data.length > 0) {
              const firstRowKeys = Object.keys(data[0])
              setDebugInfo((prev) => prev + `\nCSV字段名: ${firstRowKeys.join(", ")}`)
            }

            let totalCount = 0
            let withRankCount = 0
            let withScoreCount = 0
            let processedCount = 0

            const processedData: UniversityData[] = data
              .filter((row) => row && Object.keys(row).length > 0)
              .map((row, index) => {
                totalCount++

                // 根据新的Schema进行字段映射
                const universityName = row["学校名称"] || row["院校名称"] || row["大学名称"] || ""
                const universityCode = row["学校代号"] || row["院校代码"] || row["学校代码"] || ""
                const majorName = row["专业名称"] || row["专业"] || ""
                const majorCode = row["专业代号"] || row["专业代码"] || ""

                // 处理位次和分数 - 根据Schema，这些字段应该存在
                const rankStr = row["位次"] || row["最低位次"] || ""
                const scoreStr = row["分数线"] || row["最低分"] || row["录取分数"] || ""

                let minRank = 0
                let minScore = 0

                // 解析位次
                if (rankStr && rankStr !== "" && rankStr !== "-") {
                  const rankNum = Number(rankStr)
                  if (!isNaN(rankNum)) {
                    minRank = rankNum
                    withRankCount++
                  }
                }

                // 解析分数
                if (scoreStr && scoreStr !== "" && scoreStr !== "-") {
                  const scoreNum = Number(scoreStr)
                  if (!isNaN(scoreNum)) {
                    minScore = scoreNum
                    withScoreCount++
                  }
                }

                // 如果有分数但没有位次，设置位次为185000
                if (minScore > 0 && minRank === 0) {
                  minRank = 185000
                }

                const planCount = row["计划数"] || row["招生计划"] || ""

                // 调试前几行数据
                if (index < 5) {
                  setDebugInfo(
                    (prev) =>
                      prev +
                      `\n第${index + 1}行数据:
                    学校: ${universityName} (${universityCode})
                    专业: ${majorName} (${majorCode})
                    位次: ${rankStr} -> ${minRank}
                    分数: ${scoreStr} -> ${minScore}
                    计划: ${planCount}`,
                  )
                }

                return {
                  universityName,
                  universityCode,
                  majorName,
                  majorCode,
                  minRank,
                  minScore,
                  planCount,
                }
              })
              .filter((item) => {
                const isValid = item.universityCode && item.majorCode
                if (isValid) processedCount++
                return isValid
              })

            // 更新统计信息
            setDataStats({
              total: totalCount,
              withRank: withRankCount,
              withScore: withScoreCount,
              processed: processedCount,
            })

            setDebugInfo(
              (prev) =>
                prev +
                `\n📊 数据统计:
              总行数: ${totalCount}
              有位次的: ${withRankCount}
              有分数的: ${withScoreCount}
              最终处理: ${processedCount}`,
            )

            setCsvData(processedData)

            if (processedData.length === 0) {
              reject(new Error("CSV数据处理后没有有效记录，请检查数据格式"))
            } else {
              resolve()
            }
          } catch (err) {
            reject(new Error(`CSV数据解析失败: ${err}`))
          }
        },
        error: (error) => {
          reject(new Error(`Papa Parse错误: ${error.message}`))
        },
      })
    })
  }

  // 手动上传CSV文件
  const handleCsvUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    setCsvLoading(true)
    setError("")
    setDebugInfo("开始处理上传的CSV文件...")

    const reader = new FileReader()
    reader.onload = async (e) => {
      try {
        const csvText = e.target?.result as string
        await processCsvData(csvText)
        setDebugInfo((prev) => prev + "\n✓ CSV文件上传并处理成功")
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : String(err)
        setError(`CSV文件处理失败: ${errorMessage}`)
        setDebugInfo((prev) => prev + `\nCSV处理错误: ${errorMessage}`)
      } finally {
        setCsvLoading(false)
      }
    }

    reader.onerror = () => {
      setError("文件读取失败")
      setCsvLoading(false)
    }

    reader.readAsText(file, "UTF-8")
  }

  // 处理Excel志愿表上传
  const handleExcelUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    setLoading(true)
    setError("")

    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target?.result as ArrayBuffer)
        const workbook = XLSX.read(data, { type: "array" })
        const sheetName = workbook.SheetNames[0]
        const worksheet = workbook.Sheets[sheetName]
        const jsonData = XLSX.utils.sheet_to_json(worksheet) as any[]

        const processedVolunteers: VolunteerItem[] = jsonData
          .map((row, index) => {
            // 处理完整版格式
            if (row["院校名称"] || row["学校名称"] || row["院校代码"] || row["学校代号"]) {
              return {
                order: row["志愿序号"] || row["序号"] || index + 1,
                universityName: row["院校名称"] || row["学校名称"] || "",
                universityCode: row["院校代码"] || row["学校代号"] || "",
                majorName: row["专业名称"] || row["专业"] || "",
                majorCode: row["专业代码"] || row["专业代号"] || "",
              }
            }

            // 处理考试院版本（只有代码）
            const keys = Object.keys(row)
            const universityCodeKey = keys.find(
              (key) =>
                (key.includes("院校") && key.includes("代码")) ||
                (key.includes("学校") && key.includes("代号")) ||
                (key.includes("学校") && key.includes("代码")),
            )
            const majorCodeKey = keys.find(
              (key) => (key.includes("专业") && key.includes("代码")) || (key.includes("专业") && key.includes("代号")),
            )

            if (universityCodeKey && majorCodeKey) {
              return {
                order: index + 1,
                universityCode: row[universityCodeKey] || "",
                majorCode: row[majorCodeKey] || "",
              }
            }

            return null
          })
          .filter((item): item is VolunteerItem => item !== null && item.universityCode && item.majorCode)
          .slice(0, 80) // 最多80个志愿

        setVolunteerData(processedVolunteers)
        setError("")
      } catch (err) {
        setError("Excel文件解析失败，请检查文件格式")
      } finally {
        setLoading(false)
      }
    }

    reader.readAsArrayBuffer(file)
  }

  // 执行查询分析
  const handleQuery = async () => {
    if (!csvData.length || !volunteerData.length || !studentRank) {
      setError("请确保已上传录取数据和志愿表，并输入学生位次")
      return
    }

    setLoading(true)
    setProgress(0)
    setError("")

    const rank = Number.parseInt(studentRank)
    const score = studentScore ? Number.parseInt(studentScore) : undefined
    const queryResults: VolunteerItem[] = []

    // 添加匹配统计
    let matchedCount = 0
    let unmatchedCount = 0
    let schoolOnlyMatchedCount = 0
    const unmatchedItems: string[] = []

    // 显示CSV数据中的代码样例（前10个）
    const csvSamples = csvData
      .slice(0, 10)
      .map((item) => `${item.universityCode}-${item.majorCode} (${item.universityName}-${item.majorName})`)
      .join("\n")

    setDebugInfo((prev) => prev + `\n\n📋 CSV数据代码样例（前10个）:\n${csvSamples}`)

    for (let i = 0; i < volunteerData.length; i++) {
      const volunteer = volunteerData[i]

      // 第一步：尝试完全匹配（院校代码 + 专业代码）
      const exactMatch = csvData.find(
        (record) => record.universityCode === volunteer.universityCode && record.majorCode === volunteer.majorCode,
      )

      let result: VolunteerItem

      if (exactMatch) {
        // 完全匹配成功
        matchedCount++
        result = {
          ...volunteer,
          universityName: volunteer.universityName || exactMatch.universityName,
          majorName: volunteer.majorName || exactMatch.majorName,
          minRank: exactMatch.minRank,
          minScore: exactMatch.minScore,
          planCount: exactMatch.planCount,
          rankDiff: exactMatch.minRank ? exactMatch.minRank - rank : undefined,
          scoreDiff: exactMatch.minScore && score ? score - exactMatch.minScore : undefined,
          isAdmitted: exactMatch.minRank > 0 ? rank <= exactMatch.minRank : false,
        }
      } else {
        // 第二步：尝试只匹配院校代码，获取学校名称
        const schoolMatch = csvData.find((record) => record.universityCode === volunteer.universityCode)

        if (schoolMatch) {
          // 找到了学校但没找到专业
          schoolOnlyMatchedCount++
          result = {
            ...volunteer,
            universityName: volunteer.universityName || schoolMatch.universityName,
            majorName: volunteer.majorName || "本段计划中该专业没有人被录取",
            minRank: undefined,
            minScore: undefined,
            planCount: "本段计划中该专业没有人被录取",
            rankDiff: undefined,
            scoreDiff: undefined,
            isAdmitted: false,
          }
        } else {
          // 完全没找到匹配
          unmatchedCount++
          unmatchedItems.push(`${volunteer.universityCode}-${volunteer.majorCode}`)
          result = {
            ...volunteer,
            universityName: volunteer.universityName || "未找到院校",
            majorName: volunteer.majorName || "未找到专业",
            minRank: undefined,
            minScore: undefined,
            planCount: "未找到",
            rankDiff: undefined,
            scoreDiff: undefined,
            isAdmitted: false,
          }
        }
      }

      queryResults.push(result)
      setProgress(((i + 1) / volunteerData.length) * 100)

      // 模拟处理延迟
      await new Promise((resolve) => setTimeout(resolve, 30))
    }

    // 添加匹配统计到调试信息
    setDebugInfo(
      (prev) =>
        prev +
        `\n\n📈 匹配统计:
  ✅ 完全匹配: ${matchedCount}
  ⚠️ 仅匹配院校: ${schoolOnlyMatchedCount}
  ❌ 完全未匹配: ${unmatchedCount}
  未匹配的代码: ${unmatchedItems.slice(0, 10).join(", ")}${unmatchedItems.length > 10 ? "..." : ""}`,
    )

    setResults(queryResults)
    setLoading(false)
    setProgress(100)
  }

  // 导出结果为Excel
  const handleExport = () => {
    if (!results.length) return

    const exportData = results.map((item) => ({
      序号: item.order,
      院校代码: item.universityCode,
      院校名称: item.universityName,
      专业代码: item.majorCode,
      专业名称: item.majorName,
      录取位次: item.minRank || "未找到",
      录取分数: item.minScore || "未找到",
      计划数: item.planCount || "未找到",
      位次差值: item.rankDiff !== undefined ? item.rankDiff : "未找到",
      分数差值: item.scoreDiff !== undefined ? item.scoreDiff : "未找到",
      录取状态: item.isAdmitted ? "可录取" : "不可录取",
    }))

    try {
      const ws = XLSX.utils.json_to_sheet(exportData)
      const wb = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(wb, ws, "志愿查询结果")

      // 生成Excel文件的二进制数据
      const wbout = XLSX.write(wb, { bookType: "xlsx", type: "array" })

      // 创建Blob对象
      const blob = new Blob([wbout], { type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" })

      // 创建下载链接
      const url = URL.createObjectURL(blob)
      const link = document.createElement("a")
      link.href = url
      link.download = `浙江省2025年志愿查询结果_${new Date().toLocaleDateString()}.xlsx`

      // 触发下载
      document.body.appendChild(link)
      link.click()

      // 清理
      document.body.removeChild(link)
      URL.revokeObjectURL(url)
    } catch (err) {
      setError(`导出失败: ${err instanceof Error ? err.message : String(err)}`)
    }
  }

  const admittedCount = results.filter((r) => r.isAdmitted).length
  const totalCount = results.length

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-6xl mx-auto space-y-6">
        {/* 标题 */}
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">浙江省2025年高考志愿录取查询系统</h1>
          <p className="text-gray-600">基于2025年普通类第一段平行投档分数线表进行录取概率分析</p>
        </div>

        {/* 数据状态显示 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="w-5 h-5" />
              录取数据加载
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center gap-4">
                {csvLoading ? (
                  <div className="flex items-center gap-2 text-blue-600">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                    <span>正在加载录取数据...</span>
                  </div>
                ) : csvData.length > 0 ? (
                  <div className="flex items-center gap-2 text-green-600">
                    <CheckCircle className="w-4 h-4" />
                    <span>✓ 已加载 {csvData.length} 条录取数据</span>
                  </div>
                ) : (
                  <div className="flex items-center gap-2 text-orange-600">
                    <AlertCircle className="w-4 h-4" />
                    <span>数据加载失败，请尝试重新加载或手动上传</span>
                  </div>
                )}
              </div>

              {/* 数据加载选项 */}
              <div className="grid md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label className="text-sm font-medium">方式一：在线加载（推荐）</Label>
                  <Button
                    onClick={loadCsvData}
                    disabled={csvLoading}
                    className="w-full bg-transparent"
                    variant="outline"
                  >
                    <RefreshCw className="w-4 h-4 mr-2" />
                    从在线数据源加载
                  </Button>
                </div>
                <div className="space-y-2">
                  <Label className="text-sm font-medium">方式二：手动上传CSV文件</Label>
                  <Input
                    type="file"
                    accept=".csv"
                    onChange={handleCsvUpload}
                    disabled={csvLoading}
                    className="cursor-pointer"
                  />
                </div>
              </div>

              {/* 数据统计信息 */}
              {dataStats.total > 0 && (
                <div className="bg-blue-50 p-3 rounded text-sm">
                  <strong>数据统计:</strong>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-2 mt-2">
                    <div>总记录数: {dataStats.total}</div>
                    <div>有位次: {dataStats.withRank}</div>
                    <div>有分数: {dataStats.withScore}</div>
                    <div>有效数据: {dataStats.processed}</div>
                  </div>
                </div>
              )}

              {/* 调试信息 */}
              {debugInfo && (
                <details className="bg-gray-100 p-3 rounded">
                  <summary className="cursor-pointer text-sm font-medium">调试信息 (点击展开)</summary>
                  <div className="text-xs text-gray-600 whitespace-pre-wrap mt-2 max-h-60 overflow-y-auto">
                    {debugInfo}
                  </div>
                </details>
              )}
            </div>
          </CardContent>
        </Card>

        {/* 文件上传区域 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Upload className="w-5 h-5" />
              上传志愿表Excel
            </CardTitle>
            <CardDescription>学生填报的志愿清单，支持完整版和考试院版本</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Input type="file" accept=".xlsx,.xls" onChange={handleExcelUpload} className="cursor-pointer" />
              {volunteerData.length > 0 && (
                <div className="text-sm text-green-600">✓ 已加载 {volunteerData.length} 个志愿</div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* 学生信息输入 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Search className="w-5 h-5" />
              学生信息
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 gap-4 mb-4">
              <div>
                <Label htmlFor="rank">学生位次 *</Label>
                <Input
                  id="rank"
                  type="number"
                  placeholder="请输入学生位次，如：58000"
                  value={studentRank}
                  onChange={(e) => setStudentRank(e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="score">学生分数（可选）</Label>
                <Input
                  id="score"
                  type="number"
                  placeholder="请输入学生分数，如：650"
                  value={studentScore}
                  onChange={(e) => setStudentScore(e.target.value)}
                />
              </div>
            </div>
            <Button
              onClick={handleQuery}
              disabled={loading || csvLoading || !csvData.length || !volunteerData.length || !studentRank}
              className="w-full"
            >
              {loading ? "查询中..." : "开始查询"}
            </Button>
          </CardContent>
        </Card>

        {/* 错误提示 */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              {error}
              {error.includes("404") && (
                <div className="mt-2">
                  <p className="text-sm">建议解决方案：</p>
                  <ul className="text-sm list-disc list-inside mt-1">
                    <li>尝试手动上传CSV文件</li>
                    <li>检查网络连接</li>
                    <li>联系管理员更新数据源</li>
                  </ul>
                </div>
              )}
            </AlertDescription>
          </Alert>
        )}

        {/* 进度条 */}
        {loading && (
          <Card>
            <CardContent className="pt-6">
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>查询进度</span>
                  <span>{Math.round(progress)}%</span>
                </div>
                <Progress value={progress} className="w-full" />
              </div>
            </CardContent>
          </Card>
        )}

        {/* 查询结果 */}
        {results.length > 0 && (
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>查询结果</CardTitle>
                  <CardDescription>
                    共 {totalCount} 个志愿，其中 {admittedCount} 个可录取
                  </CardDescription>
                  {/* 添加详细匹配统计 */}
                  <div className="mt-2 text-sm text-gray-600">
                    完全匹配: {results.filter((r) => r.minRank !== undefined).length} 个， 仅匹配院校:{" "}
                    {results.filter((r) => r.minRank === undefined && r.universityName !== "未找到院校").length} 个，
                    完全未匹配: {results.filter((r) => r.universityName === "未找到院校").length} 个
                  </div>
                </div>
                <Button onClick={handleExport} className="flex items-center gap-2">
                  <Download className="w-4 h-4" />
                  导出Excel
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full border-collapse border border-gray-300">
                  <thead>
                    <tr className="bg-gray-50">
                      <th className="border border-gray-300 px-4 py-2 text-left">序号</th>
                      <th className="border border-gray-300 px-4 py-2 text-left">院校代码</th>
                      <th className="border border-gray-300 px-4 py-2 text-left">院校名称</th>
                      <th className="border border-gray-300 px-4 py-2 text-left">专业代码</th>
                      <th className="border border-gray-300 px-4 py-2 text-left">专业名称</th>
                      <th className="border border-gray-300 px-4 py-2 text-left">录取位次</th>
                      <th className="border border-gray-300 px-4 py-2 text-left">录取分数</th>
                      <th className="border border-gray-300 px-4 py-2 text-left">计划数</th>
                      <th className="border border-gray-300 px-4 py-2 text-left">位次差值</th>
                      <th className="border border-gray-300 px-4 py-2 text-left">分数差值</th>
                      <th className="border border-gray-300 px-4 py-2 text-left">录取状态</th>
                    </tr>
                  </thead>
                  <tbody>
                    {results.map((item) => (
                      <tr
                        key={`${item.order}-${item.universityCode}-${item.majorCode}`}
                        className={item.isAdmitted ? "bg-blue-50" : ""}
                      >
                        <td className="border border-gray-300 px-4 py-2">{item.order}</td>
                        <td className="border border-gray-300 px-4 py-2">{item.universityCode}</td>
                        <td
                          className={`border border-gray-300 px-4 py-2 ${item.isAdmitted ? "font-bold text-blue-700" : ""}`}
                        >
                          {item.universityName}
                        </td>
                        <td className="border border-gray-300 px-4 py-2">{item.majorCode}</td>
                        <td
                          className={`border border-gray-300 px-4 py-2 ${item.isAdmitted ? "font-bold text-blue-700" : ""}`}
                        >
                          {item.majorName}
                        </td>
                        <td className="border border-gray-300 px-4 py-2">
                          {item.minRank ? (item.minRank === 185000 ? "185000*" : item.minRank) : "未找到"}
                        </td>
                        <td className="border border-gray-300 px-4 py-2">{item.minScore || "未找到"}</td>
                        <td className="border border-gray-300 px-4 py-2">{item.planCount || "未找到"}</td>
                        <td
                          className={`border border-gray-300 px-4 py-2 ${
                            item.rankDiff !== undefined
                              ? item.rankDiff >= 0
                                ? "text-red-600 font-semibold"
                                : "text-green-600 font-semibold"
                              : ""
                          }`}
                        >
                          {item.rankDiff !== undefined
                            ? item.rankDiff >= 0
                              ? `+${item.rankDiff}`
                              : item.rankDiff
                            : "未找到"}
                        </td>
                        <td
                          className={`border border-gray-300 px-4 py-2 ${
                            item.scoreDiff !== undefined
                              ? item.scoreDiff >= 0
                                ? "text-red-600 font-semibold"
                                : "text-green-600 font-semibold"
                              : ""
                          }`}
                        >
                          {item.scoreDiff !== undefined
                            ? item.scoreDiff >= 0
                              ? `+${item.scoreDiff}`
                              : item.scoreDiff
                            : studentScore
                              ? "未找到"
                              : "-"}
                        </td>
                        <td
                          className={`border border-gray-300 px-4 py-2 ${item.isAdmitted ? "font-bold text-blue-700" : "text-gray-500"}`}
                        >
                          {item.isAdmitted
                            ? "✓ 可录取"
                            : item.majorName === "本段计划中该专业没有人被录取"
                              ? "⚠️ 本段计划中该专业没有人被录取"
                              : item.universityName === "未找到院校"
                                ? "❌ 未找到院校信息"
                                : "✗ 不可录取"}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
